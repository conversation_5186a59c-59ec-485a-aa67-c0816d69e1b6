[project]
name = "face-validation"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.115.12",
    "faststream>=0.5.42",
    "httptools>=0.6.4",
    "httpx>=0.28.1",
    "insightface>=0.7.3",
    "loguru>=0.7.3",
    "onnxruntime>=1.22.0",
    "opencv-python>=*********",
    "pillow>=11.2.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "uvloop>=0.21.0",
]

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"
